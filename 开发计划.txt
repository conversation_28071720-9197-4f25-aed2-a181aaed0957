OWASP dep-scan 前后端分离应用开发计划 (非容器化)
1. 项目目标

开发一个企业级的、前后端分离的依赖项扫描平台。该平台以前端提供统一的操作界面，后端调度一个由多个 dep-scan 进程组成的扫描池，为开发团队提供一个简单、高效、集中的 SBOM 漏洞扫描服务。
2. 核心功能

    文件上传：用户可以通过浏览器界面，通过点击或拖拽方式上传 SBOM (CycloneDX JSON 格式) 文件。

    启动扫描：后端接收文件后，自动选择一个空闲的 dep-scan 进程进行扫描。

    实时状态：前端界面能实时显示扫描任务的状态（排队中、扫描中、已完成、失败）。

    报告展示：扫描完成后，对 dep-scan 输出的 JSON 报告进行解析和可视化，以前端图表和表格的形式清晰地展示漏洞信息。

    历史记录：可以查看最近的扫描历史列表。

3. 开发阶段与任务分解
阶段一：后端开发与环境准备 (预计时间：1.5周)

这个阶段的目标是搭建起核心的服务能力。

    任务 1.1: 准备 dep-scan 扫描环境

        动作: 在服务器上，通过命令行手动启动多个 dep-scan 进程，并让它们在不同的端口上以服务器模式运行。

        技术细节:

            为保证进程在后台持续运行，可以使用 nohup 或 systemd 服务。

            示例命令：

            nohup depscan --server --server-host 0.0.0.0 --server-port 7071 &
            nohup depscan --server --server-host 0.0.0.0 --server-port 7072 &

        产出: 一个由多个正在运行的 dep-scan 进程组成的、可供调用的扫描池。

    任务 1.2: 开发核心后端 API 服务

        技术选型:

            语言/框架: 推荐 Python + FastAPI。FastAPI 性能高，异步支持好，自动生成 API 文档，非常适合 I/O 密集型任务。

        API 端点 (Endpoints) 定义:

            POST /api/upload: 接收 SBOM 文件上传。将文件保存在服务器临时目录，并返回一个唯一的文件 ID。

            POST /api/scan: 接收文件 ID，将扫描任务放入一个内部队列。

            GET /api/scans/{scan_id}: 根据扫描任务 ID，查询其状态（queued, scanning, completed, failed）和结果。

            GET /api/scans: 获取历史扫描任务列表。

        扫描调度逻辑:

            在后端应用中，维护一个 dep-scan 进程的地址列表（例如 ['http://localhost:7071', 'http://localhost:7072']）和它们的状态（idle/busy）。

            实现一个简单的任务队列和调度器，按顺序从队列中取出任务，分配给状态为 idle 的实例。

        产出: 一套功能完整的、带 OpenAPI (Swagger) 文档的后端服务。

阶段二：前端开发 (预计时间：1.5周)

这个阶段专注于打造用户界面和体验。

    任务 2.1: 技术选型与项目搭建

        技术选型:

            框架: 推荐 React 或 Vue。

            UI 组件库: Ant Design 或 Material-UI (MUI)，可以快速构建出专业且美观的界面。

            图表库: Recharts 或 ECharts，用于展示漏洞统计图表。

            构建工具: Vite。

        产出: 一个标准化的前端项目结构。

    任务 2.2: 界面开发

        主页/上传页:

            页面中央放置一个醒目的拖拽上传区域。

            下方或侧边栏显示最近的扫描历史列表。

        结果展示页:

            顶部: 扫描任务的摘要信息（文件名、扫描时间、漏洞总数等）和状态指示器。

            中部: 使用卡片和图表（如饼图）展示漏洞等级分布（严重、高、中、低）。

            下部: 使用可搜索、可排序的表格详细列出每一个漏洞信息（CVE-ID, 组件名, 版本, 严重性等）。

        产出: 静态的前端页面和组件。

阶段三：集成与部署 (预计时间：1周)

将前后端连接起来，并完成部署。

    任务 3.1: 前后端 API 集成

        动作: 在前端应用中调用后端 API，实现文件上传、启动扫描和获取结果的完整流程。

        关键技术: 实现异步轮询。当前端提交扫描任务后，定期（如每3秒）调用 GET /api/scans/{scan_id} 接口来更新任务状态，直到状态变为 completed 或 failed。

    任务 3.2: 应用部署

        后端部署:

            使用 uvicorn 或 gunicorn 来运行 FastAPI 应用。

            示例命令: uvicorn main:app --host 0.0.0.0 --port 8000

        前端部署:

            运行 npm run build 或 yarn build 来生成包含 index.html 的静态文件包。

            使用 Nginx 或其他 Web 服务器来托管这些静态文件。

        Nginx 反向代理配置:

            配置 Nginx 作为统一入口，将对 /api/ 路径的请求转发到后端运行的 FastAPI 服务上（http://localhost:8000）。

            将所有其他请求（/）指向前端静态文件的存放目录。

        产出: 一个完整可用的、部署在服务器上的 Web 应用。

4. 优化与未来迭代方向

    数据库集成: 将扫描历史和结果保存到数据库（如 PostgreSQL 或 SQLite）中，实现持久化存储和更复杂的查询。

    用户认证: 引入用户登录和权限系统，实现多租户隔离。

    Webhook 通知: 扫描完成后，通过 Webhook (如通知到 Slack、钉钉、Teams) 主动推送结果。

    CI/CD 集成: 提供 API Key，允许 CI/CD 流水线直接调用 API 来触发扫描。

这个更新后的计划完全聚焦于应用开发本身，为您提供了一个清晰的路线图。祝您项目顺利！